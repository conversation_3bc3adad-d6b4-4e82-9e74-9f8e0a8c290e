#!/bin/bash

# Oracle ORAPP Test Script
# Tests the ORAPP library against Oracle NEO223 service with neontk/neontk account

# Set Oracle environment variables
export LD_LIBRARY_PATH=/usr/lib/oracle/21/client64/lib
export ORACLE_HOME=/usr/lib/oracle/21/client64
export TNS_ADMIN=/usr/lib/oracle/21/client64/network/admin

# Default connection parameters
TNS_NAME="NEO223"
USERNAME="neontk"
PASSWORD="neontk"
CONTINUOUS=false

# Function to display usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -T <tns_name>   TNS service name (default: NEO223)"
    echo "  -U <username>   Database username (default: neontk)"
    echo "  -P <password>   Database password (default: neontk)"
    echo "  -c              Run test continuously (until Ctrl+C)"
    echo "  -h              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with default settings"
    echo "  $0 -T NEO226 -U testuser -P testpass # Run with custom connection"
    echo "  $0 -c                                 # Run continuously"
}

# Parse command line arguments
while getopts "T:U:P:ch" opt; do
    case $opt in
        T) TNS_NAME="$OPTARG" ;;
        U) USERNAME="$OPTARG" ;;
        P) PASSWORD="$OPTARG" ;;
        c) CONTINUOUS=true ;;
        h) usage; exit 0 ;;
        *) usage; exit 1 ;;
    esac
done

# Change to the orapp directory
cd "$(dirname "$0")"

# Check if test executable exists
if [ ! -f "./test" ]; then
    echo "Error: test executable not found. Building..."
    if ! make test; then
        echo "Error: Failed to build test executable"
        exit 1
    fi
fi

# Display test information
echo "=========================================="
echo "Oracle ORAPP Test"
echo "=========================================="
echo "TNS Service: $TNS_NAME"
echo "Username:    $USERNAME"
echo "Password:    $PASSWORD"
echo "Continuous:  $CONTINUOUS"
echo "=========================================="
echo ""

# Build test command
TEST_CMD="./test -T $TNS_NAME -U $USERNAME -P $PASSWORD"
if [ "$CONTINUOUS" = true ]; then
    TEST_CMD="$TEST_CMD -c"
fi

# Run the test
echo "Running ORAPP test..."
echo "Command: $TEST_CMD"
echo ""

exec $TEST_CMD
