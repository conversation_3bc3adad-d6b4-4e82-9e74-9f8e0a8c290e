#include <iostream>
#include <string>
#include <cstring>
#include <unistd.h>

extern "C" {
#include <oci.h>
}

#include "orapp.hh"

using namespace std;

void logfunc(const char *s) {
    cout << "[ORAPP LOG] " << s << endl;
}

void test_proc_check_mms_login_ext2(ORAPP::Connection &db, const char* cid, const char* pwd) {
    cout << "\n=== Testing proc_check_mms_login_ext2 ===" << endl;
    cout << "CID: " << cid << endl;
    cout << "PWD: " << pwd << endl;

    ORAPP::Query *q = db.query();
    if (!q) {
        cout << "ERROR: Failed to create query object" << endl;
        return;
    }

    // Variable declarations (use larger buffers for safety during OUT binds)
    char szSIP[256];             // client company's server IP list
    char szAPPName[64];          // appname for report
    char szErrMsg[2048];         // procedure result string
    int  nmPID;                  // client company pid
    int  nmJOB;                  // client company jobcode
    int  nmPRT;                  // client company priority
    int  nmCNT;                  // sender Sleep: rest time after sending one message
    int  nmRST;                  // succ: 0 fail: negative -2: no data -1: others
    int  nUrlJob;                // jobcode when using url: -99 when not used
    int  nRptWait;               // report Sleep: rest time after sending one message

    char szServerInfo[512];      // xxx.xxx.xxx.xxx:xxxxx
    int  nRptNoDataSleep;        // default 3 sec
    char szSenderName[256];      // relative path from home directory
    char szReportName[256];      // relative path from home directory
    char szSenderDBName[256];    // relative path from home directory localhost or ip:port
    char szReportDBName[256];    // relative path from home directory localhost or ip:port
    char szLogFilePath[512];     // full path
    char szReserve[512];         // additional reserved field etc

    // Initialize variables
    memset(szSIP, 0x00, sizeof(szSIP));
    memset(szAPPName, 0x00, sizeof(szAPPName));
    memset(szErrMsg, 0x00, sizeof(szErrMsg));
    memset(szServerInfo, 0x00, sizeof(szServerInfo));
    memset(szSenderName, 0x00, sizeof(szSenderName));
    memset(szReportName, 0x00, sizeof(szReportName));
    memset(szSenderDBName, 0x00, sizeof(szSenderDBName));
    memset(szReportDBName, 0x00, sizeof(szReportDBName));
    memset(szLogFilePath, 0x00, sizeof(szLogFilePath));
    memset(szReserve, 0x00, sizeof(szReserve));

    nRptNoDataSleep = 3;
    nmRST = -999;

    // Build the stored procedure call exactly as in logonDB.cpp
    *q << "BEGIN "
        << "proc_check_mms_login_ext2("
        << "in_cid=>'" << cid << "',"
        << "in_pwd=>'" << pwd << "',"
        << "ot_appname=>:szAPPName,"
        << "ot_sip=>:szSIP,"
        << "ot_pid=>:nmPID,"
        << "ot_job=>:nmJOB,"                /* company business code*/
        << "ot_c_job=>:nUrlJob,"            /* callback jobcode */
        << "ot_prt=>:nmPRT,"                /* message priority*/
        << "ot_cnt=>:nmCNT,"                /* transmission count per second*/
        << "ot_rpt_cnt=>:nRptWait,"         /* sleep time when sending continuous rpt */
        << "ot_server_info=>:szServerInfo,"
        << "ot_rpt_sleep_cnt=>:nRptNoDataSleep,"
        << "ot_sender_proc=>:szSenderName,"
        << "ot_report_proc=>:szReportName,"
        << "ot_senderdb_info=>:szSenderDBName,"
        << "ot_reportdb_info=>:szReportDBName,"
        << "ot_logfile_info=>:szLogFilePath,"
        << "ot_etc=>:szReserve,"
        << "ot_rst=>:nmRST,"                /* execution result    */
        << "ot_rstmsg=>:szErrMsg);"         /* sql err msg */
        << "END;";

    cout << "\nSQL Statement built successfully" << endl;

    // Bind parameters and mark OUT/INOUT as needed
    q->bind(":szAPPName", szAPPName, sizeof(szAPPName)); q->set_bind_output(":szAPPName");
    q->bind(":szSIP", szSIP, sizeof(szSIP));             q->set_bind_output(":szSIP");
    q->bind(":nmPID", nmPID);                             q->set_bind_output(":nmPID");
    q->bind(":nmJOB", nmJOB);                             q->set_bind_output(":nmJOB");
    q->bind(":nUrlJob", nUrlJob);                         q->set_bind_output(":nUrlJob");
    q->bind(":nmPRT", nmPRT);                             q->set_bind_output(":nmPRT");
    q->bind(":nmCNT", nmCNT);                             q->set_bind_output(":nmCNT");
    q->bind(":nRptWait", nRptWait);                       q->set_bind_output(":nRptWait");
    q->bind(":szServerInfo", szServerInfo, sizeof(szServerInfo)); q->set_bind_output(":szServerInfo");
    q->bind(":nRptNoDataSleep", nRptNoDataSleep);         q->set_bind_output(":nRptNoDataSleep");
    q->bind(":szSenderName", szSenderName, sizeof(szSenderName)); q->set_bind_output(":szSenderName");
    q->bind(":szReportName", szReportName, sizeof(szReportName)); q->set_bind_output(":szReportName");
    q->bind(":szSenderDBName", szSenderDBName, sizeof(szSenderDBName)); q->set_bind_output(":szSenderDBName");
    q->bind(":szReportDBName", szReportDBName, sizeof(szReportDBName)); q->set_bind_output(":szReportDBName");
    q->bind(":szLogFilePath", szLogFilePath, sizeof(szLogFilePath)); q->set_bind_output(":szLogFilePath");
    q->bind(":szReserve", szReserve, sizeof(szReserve));  q->set_bind_output(":szReserve");
    q->bind(":nmRST", nmRST);                             q->set_bind_output(":nmRST");
    q->bind(":szErrMsg", szErrMsg, sizeof(szErrMsg));     q->set_bind_output(":szErrMsg");

    cout << "Parameters bound successfully" << endl;

    // Set alarm timeout like in original code
    alarm(60);

    cout << "\nExecuting stored procedure..." << endl;
    if (!q->execute()) {
        cout << "ERROR: Stored procedure execution failed - " << db.error() << endl;
        alarm(0);
        return;
    }
    alarm(0);

    cout << "Stored procedure executed successfully!" << endl;

    // Display results
    cout << "\n=== RESULTS ===" << endl;
    cout << "nmRST (Result Code): " << nmRST << endl;
    cout << "szErrMsg: " << szErrMsg << endl;
    cout << "szAPPName: " << szAPPName << endl;
    cout << "szSIP: " << szSIP << endl;
    cout << "nmPID: " << nmPID << endl;
    cout << "nmJOB: " << nmJOB << endl;
    cout << "nUrlJob: " << nUrlJob << endl;
    cout << "nmPRT: " << nmPRT << endl;
    cout << "nmCNT: " << nmCNT << endl;
    cout << "nRptWait: " << nRptWait << endl;
    cout << "szServerInfo: " << szServerInfo << endl;
    cout << "nRptNoDataSleep: " << nRptNoDataSleep << endl;
    cout << "szSenderName: " << szSenderName << endl;
    cout << "szReportName: " << szReportName << endl;
    cout << "szSenderDBName: " << szSenderDBName << endl;
    cout << "szReportDBName: " << szReportDBName << endl;
    cout << "szLogFilePath: " << szLogFilePath << endl;
    cout << "szReserve: " << szReserve << endl;

    // Interpret result code
    cout << "\n=== RESULT INTERPRETATION ===" << endl;
    if (nmRST == 0) {
        cout << "SUCCESS: Login authentication successful" << endl;
    } else if (nmRST == -2) {
        cout << "FAILURE: No data found (user not found)" << endl;
    } else if (nmRST == -1) {
        cout << "FAILURE: Other error occurred" << endl;
    } else {
        cout << "UNKNOWN: Unexpected result code: " << nmRST << endl;
    }
}


int main(int argc, char* argv[]) {
    cout << "===========================================" << endl;
    cout << "Oracle ORAPP proc_check_mms_login_ext2 Test" << endl;
    cout << "===========================================" << endl;

    // Default connection parameters
    string tns = "NEO223";
    string username = "neontk";
    string password = "neontk";
    string test_cid = "ntalktest1";
    string test_pwd = "ntalktest1";

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-T") == 0 && i + 1 < argc) {
            tns = argv[++i];
        } else if (strcmp(argv[i], "-U") == 0 && i + 1 < argc) {
            username = argv[++i];
        } else if (strcmp(argv[i], "-P") == 0 && i + 1 < argc) {
            password = argv[++i];
        } else if (strcmp(argv[i], "-C") == 0 && i + 1 < argc) {
            test_cid = argv[++i];
        } else if (strcmp(argv[i], "-W") == 0 && i + 1 < argc) {
            test_pwd = argv[++i];
        } else if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            cout << "Usage: " << argv[0] << " [options]" << endl;
            cout << "Options:" << endl;
            cout << "  -T <tns>      TNS service name (default: NEO223)" << endl;
            cout << "  -U <user>     Database username (default: neontk)" << endl;
            cout << "  -P <pass>     Database password (default: neontk)" << endl;
            cout << "  -C <cid>      Test CID (default: ntalktest1)" << endl;
            cout << "  -W <pwd>      Test password (default: ntalktest1)" << endl;
            cout << "  -h, --help    Show this help message" << endl;
            return 0;
        }
    }

    cout << "TNS Service: " << tns << endl;
    cout << "Username:    " << username << endl;
    cout << "Password:    " << password << endl;
    cout << "Test CID:    " << test_cid << endl;
    cout << "Test PWD:    " << test_pwd << endl;
    cout << "===========================================" << endl;

    // Set up ORAPP logging
    ORAPP::log_to(logfunc);

    // Create connection
    ORAPP::Connection db;

    cout << "\nConnecting to Oracle..." << endl;
    if (!db.connect(tns.c_str(), username.c_str(), password.c_str())) {
        cout << "ERROR: Failed to connect to database" << endl;
        cout << "Error: " << db.error() << endl;
        return 1;
    }

    cout << "Connected successfully!" << endl;
    cout << "Server version: " << db.version() << endl;

    // Test the stored procedure
    test_proc_check_mms_login_ext2(db, test_cid.c_str(), test_pwd.c_str());

    // Disconnect
    cout << "\nDisconnecting..." << endl;
    if (!db.disconnect()) {
        cout << "WARNING: Failed to disconnect cleanly" << endl;
        cout << "Error: " << db.error() << endl;
    } else {
        cout << "Disconnected successfully!" << endl;
    }

    cout << "\n=== Test completed ===" << endl;
    return 0;
}
