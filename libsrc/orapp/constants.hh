#ifndef ORAPP_CONSTANTS_HH
#define ORAPP_CONSTANTS_HH

/*
 * $Id: constants.hh,v 1.1.1.1 2007/05/07 00:24:44 neosms Exp $
 */

extern "C" {
#include <oci.h>
}

#define ORAPP_INFO_BUFSIZ          1024
#define ORAPP_ERR_BUFSIZ           1024
#define ORAPP_FORMATSTR_BUFSIZ     1024
#define ORAPP_MAX_FIELD_WIDTH      100


// Provide defaults for in/out mode constants if not present in this OCI version
#ifndef OCI_INPUT
#define OCI_INPUT 1
#endif
#ifndef OCI_OUTPUT
#define OCI_OUTPUT 2
#endif
#ifndef OCI_INOUT
#define OCI_INOUT 3
#endif

#define ORAPP_SUCCESS(s) (s == OCI_SUCCESS || s == OCI_SUCCESS_WITH_INFO)

#ifndef MIN
#define MIN(a,b) ((a) < (b) ? (a) : (b))
#endif

#ifndef MAX
#define MAX(a,b) ((a) > (b) ? (a) : (b))
#endif

namespace ORAPP {
    extern const char *VERSION;
};

#endif
