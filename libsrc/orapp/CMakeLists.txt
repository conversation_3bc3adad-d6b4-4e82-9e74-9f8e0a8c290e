cmake_minimum_required(VERSION 3.16)
project(orapp)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS ON)  # Enable GNU extensions for gnu++11
set(CMAKE_C_STANDARD 99)

# Oracle environment check
if(NOT DEFINED ENV{ORACLE_HOME})
    message(FATAL_ERROR "ORACLE_HOME environment variable is not set")
endif()

set(ORACLE_HOME $ENV{ORACLE_HOME})

# Detect Oracle installation type and set appropriate include paths
if(EXISTS "${ORACLE_HOME}/rdbms/demo" AND EXISTS "${ORACLE_HOME}/rdbms/public")
    # Full Oracle installation (production environment)
    message(STATUS "Detected Full Oracle installation")
    set(ORACLE_INCLUDE_DIRS
        ${ORACLE_HOME}/rdbms/demo
        ${ORACLE_HOME}/rdbms/public
    )
else()
    # Oracle Instant Client (development environment)
    message(STATUS "Detected Oracle Instant Client installation")
    set(PROC_INCLUDE "/usr/include/oracle/21/client64")
    if(NOT EXISTS ${PROC_INCLUDE})
        # Try alternative paths for different Oracle versions
        set(PROC_INCLUDE_ALTERNATIVES
            "/usr/include/oracle/19/client64"
            "/usr/include/oracle/18/client64"
            "/usr/include/oracle/12.2/client64"
            "/usr/include/oracle/12.1/client64"
        )
        foreach(alt_path ${PROC_INCLUDE_ALTERNATIVES})
            if(EXISTS ${alt_path})
                set(PROC_INCLUDE ${alt_path})
                break()
            endif()
        endforeach()
    endif()

    if(NOT EXISTS ${PROC_INCLUDE})
        message(FATAL_ERROR "Oracle Instant Client headers not found. Please install oracle-instantclient-devel package.")
    endif()

    set(ORACLE_INCLUDE_DIRS ${PROC_INCLUDE})
endif()

# Compile flags setting (reflecting Makefile's GEN_FLAGS)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64 -fno-exceptions -fno-rtti -D_REENTRANT=1 -O2 -w")

# Include directory setting
include_directories(
    ${ORACLE_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Library directory setting
link_directories(
    ${ORACLE_HOME}/lib
)

# Source files definition
set(ORAPP_SOURCES
    conn.cc
    constants.cc
    error.cc
    field.cc
    log.cc
    query.cc
    row.cc
    ctype.cc
)

# Header files definition
set(ORAPP_HEADERS
    conn.hh
    constants.hh
    error.hh
    field.hh
    log.hh
    query.hh
    row.hh
    orapp.hh
)

# orapp static library creation
add_library(liborapp STATIC ${ORAPP_SOURCES})
set_target_properties(liborapp PROPERTIES OUTPUT_NAME orapp)

# Oracle library linking
target_link_libraries(liborapp 
    clntsh
    dl
)

# Output directory setting (create in current directory)
set_target_properties(liborapp PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Test executable (optional)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test.cc)
    add_executable(orapp_test test.cc)
    target_link_libraries(orapp_test 
        liborapp
        clntsh
        dl
        pthread
    )
    set_target_properties(orapp_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        OUTPUT_NAME test
    )
endif()

# Target to install to $HOME/library
add_custom_target(install_orapp
    COMMAND ${CMAKE_COMMAND} -E make_directory $ENV{HOME}/library
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.a $ENV{HOME}/library/
    # Copy header files
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/conn.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/constants.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/error.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/field.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/log.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/query.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/row.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/orapp.hh $ENV{HOME}/library/
    DEPENDS liborapp
    COMMENT "Installing orapp library and headers to $HOME/library"
)

# clean target
add_custom_target(clean_orapp
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/*.o
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.a
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.so
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/test
    COMMENT "Cleaning orapp build artifacts"
)
