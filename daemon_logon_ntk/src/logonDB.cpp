#include "logonDB.h"
#include "string.h"
#include <iostream>
using namespace std;

void logfunc(const char *s) 
{
    log_history(0,0,"[%s]",s);
}


int main(int argc, char* argv[])
{
    int ret;
    int hNewSocket;
    char logMsg[SOCKET_BUFF];
    char buff[DOMAIN_RECV_MAX];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    
	TypeMsgBindSnd* pLogonData;

    ORAPP::log_to(logfunc);
    ORAPP::Connection db;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) 
    {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    ret = configParse(argv[1]);
    
    if( ret != 0 )
    {
        printf("err\n");
        ml_sub_end();
        exit(1);
    }

    printf("[%s][%s]\n",PROCESS_NAME,gConf.logonDBName);

    sprintf(logMsg,"start [%s]",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();

    log_history(0,0,"[INF] creating domain socket - [%s]", gConf.logonDBName);
    ret = svrSockfd.createDomainNon(gConf.logonDBName);
    if( ret !=0 )
    {
        log_history(0,0,"[ERR] socket_domain logonDB create failed - [%s] - %s", gConf.logonDBName, strerror(errno));
        goto END;
    }
    log_history(0,0,"[INF] domain socket created successfully - [%s]", gConf.logonDBName);

    if (!orapp_connect(db, gConf.dbSID, gConf.dbID,gConf.dbPASS ))
    {
        log_history(0,0,"[ERR] db connect failed\n- SID[%s]\n- ID[%s]\n- PW[%s]");
        goto END;
    }

    while(activeProcess)
    {
    		// 20170621 select ADD
        hNewSocket = -1;

        // select func
        wait_a_moment(10000);

        ret = svrSockfd.select(0,50000);

        if(ret > 0)
        {
            hNewSocket = svrSockfd.accept();
            log_history(0,0,"[INF] domain socket connection accepted - [%s] socket_fd[%d]", gConf.logonDBName, hNewSocket);
        }

        if( hNewSocket <= 0 )
        {
            continue;
        }

        // new connection
        newSockfd.attach(hNewSocket);
        memset(buff,0x00,sizeof(buff));

        log_history(0,0,"[INF] attempting to read from domain socket - [%s]", gConf.logonDBName);
        ret = newSockfd.rcvmsg(buff);
    	int e = errno;
    	log_history(0, 0, "[DBG] rcvmsg ret[%d] errno[%d](%s) getErrMsg(%s)",
    		ret, e, strerror(e), newSockfd.getErrMsg());
    	
        log_history(0,0,"[INF] domain socket read result - bytes[%d] data[%s]", ret, ret > 0 ? buff : "");
        if( ret == 0 )
        {
            newSockfd.close();
            log_history(0,0,"[ERR] socket_domain logonDB read failed - close by peer - [%s]", gConf.logonDBName);
            continue;
        }

        if( ret < 0 )
        {
            newSockfd.close();
            log_history(0,0,"[ERR] socket_domain logonDB read failed - read Error - [%s] - %s", gConf.logonDBName, strerror(errno));
        	log_history(0, 0, "[ERR] read -1; errno[%d](%s) getErrMsg[%s]",
        		e, strerror(e), newSockfd.getErrMsg());
            continue;
        }


/* admin communication */
        viewPack(buff,ret);
        if( memcmp(buff,"getInfo",7) == 0 )
        {
            newSockfd.send("1",2);
            newSockfd.close();
            continue;
        }
/* admin communication */
        classifyProtocol(db,newSockfd,ret,buff);
        newSockfd.close();
    }

END:
    svrSockfd.close();
    orapp_disconnect(db);
    sprintf(logMsg,"close [%s]",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass) 
{

    if (!db.connect(tns, user, pass)) 
    {
        log_history(0,0,"[ERR] db connect failed - [%s]", db.error().c_str());
        return false;
    }
    log_history(0,0,"[INF] db connect success - ver [%s]", db.version().c_str());

    return true;
}

bool orapp_disconnect(ORAPP::Connection &db) {
    if (!db.disconnect()) 
    {
        log_history(0,0,"[ERR] db disconnect failed");
        return false;
    }
		log_history(0,0,"db disconnect success");
    return true;
}

int checkLogon(ORAPP::Connection &db,char* buff,int type) {

    log_history(0,0,"[DEBUG] checkLogon function started - type[%d]", type);
    CPacketUtil packetUtil;
    log_history(0,0,"[DEBUG] CPacketUtil created");
    ORAPP::Query *q = db.query();
    log_history(0,0,"[DEBUG] ORAPP::Query created");
    CLogonDbInfo logonDbInfo;
    log_history(0,0,"[DEBUG] CLogonDbInfo created");

    char szSIP[100+1]; /* client company's server IP list */
    char szAPPName[16]; /* appname for report */
    char szErrMsg[512]; /* procedure result string */
    int  nmPID; /* client company pid */
    int  nmJOB;  /* client company jobcode */
    int  nmPRT; /* client company priority */
    int  nmCNT;  /* sender Sleep : rest time after sending one message  */
    int  nmRST; /* succ : 0 fail: negative -2 : no data -1 : others */
    int  nUrlJob; /* jobcode when using url : -99 when not used */
    int  nRptWait; /* report Sleep : rest time after sending one message */

    char szServerInfo[128]; /* xxx.xxx.xxx.xxx:xxxxx => 21 + 1(|:delimiter) = 22 * 5 = 110 approximately 128 */
    int  nRptNoDataSleep; /* default 3 sec */
    char szSenderName[64]; /* relative path from home directory */
    char szReportName[64]; /* relative path from home directory */
    char szSenderDBName[64]; /* relative path from home directory localhost or ip:port */
    char szReportDBName[64]; /* relative path from home directory localhost or ip:port */
    char szLogFilePath[128]; /* full path */
    char szReserve[128]; /* additional reserved field etc */

		//* < brief declaration of variables related to sending limit
	ORAPP::Query *q2 = db.query();
	char szLimitType[1+1];
	char szLimitFlag[1+1];
	int  nDayWarnCnt;
	int  nMonWarnCnt;
	int  nDayLimitCnt;
	int  nMonLimitCnt;
	int  nDayAccCnt;
	int  nMonAccCnt;
		
	char szPID[9];
    char szCID[16];
    char szPWD[16];
    char szClassify[4];

    memset(szCID		,0x00	,sizeof(szCID));
    memset(szPWD		,0x00	,sizeof(szPWD));
    memset(szClassify	,0x00	,sizeof(szClassify));
	TypeMsgBindSnd* pSend = (TypeMsgBindSnd*)buff;
	if (type == 1)
	{
		printf(buff);
		log_history(0,0,"[DEBUG] Before parsing - buff[%s]", buff);
		packetUtil.findValue(buff,"ID",szCID);
		log_history(0,0,"[DEBUG] After ID parsing - szCID[%s]", szCID);
		packetUtil.findValue(buff,"PASSWORD",szPWD);
		log_history(0,0,"[DEBUG] After PASSWORD parsing - szPWD[%s]", szPWD);
		packetUtil.findValue(buff,"REPORT",szClassify);
		log_history(0,0,"[DEBUG] After REPORT parsing - szClassify[%s]", szClassify);
		memset(&logonDbInfo,0x00,sizeof(logonDbInfo)); 
		memcpy(logonDbInfo.szCID,szCID,sizeof(szCID)-1);
		memcpy(logonDbInfo.szPWD,szPWD,sizeof(szPWD)-1);
		log_history(0,0,"ID[%s] PASSWORD[%s] REPORT[%s]",szCID,szPWD,szClassify);
	}
	else
	{
		memcpy(logonDbInfo.szCID,pSend->szCID,sizeof(pSend->szCID));
		memcpy(logonDbInfo.szPWD,pSend->szPWD,sizeof(pSend->szPWD));
//		log_history(0,0,"volume conversion ID[%s] PASSWORD[%s] REPORT[%s]",logonDbInfo.szCID,logonDbInfo.szPWD,szClassify);
		log_history(0,0,"volume conversion ID[] PASSWORD[] REPORT[%s]",szClassify);
	}

    trim(logonDbInfo.szCID,strlen(logonDbInfo.szCID));
    trim(logonDbInfo.szPWD,strlen(logonDbInfo.szPWD));

	memset(szSIP			,0x00	,sizeof(szSIP));			//CCL(szSIP);
    memset(szAPPName		,0x00	,sizeof(szAPPName));		//CCL(szAPPName);
    memset(szErrMsg			,0x00	,sizeof(szErrMsg));			//CCL(szErrMsg);

    memset(szServerInfo		,0x00	,sizeof(szServerInfo));		//CCL(szServerInfo);
    memset(szSenderName		,0x00	,sizeof(szSenderName));		//CCL(szSenderName);
    memset(szReportName		,0x00	,sizeof(szReportName));		//CCL(szReportName);
    memset(szSenderDBName	,0x00	,sizeof(szSenderDBName));	//CCL(szSenderDBName);
    memset(szReportDBName	,0x00	,sizeof(szReportDBName));	//CCL(szReportDBName);
    memset(szLogFilePath	,0x00	,sizeof(szLogFilePath));	//CCL(szLogFilePath);
    memset(szReserve		,0x00	,sizeof(szReserve));		//CCL(szReserve);
    
	nRptNoDataSleep = 3;
    nmRST = -999;

    *q << "BEGIN "
        << "proc_check_mms_login_ext2("
        << "in_cid=>'" << logonDbInfo.szCID << "',"
        << "in_pwd=>'" << logonDbInfo.szPWD << "',"
        << "ot_appname=>:szAPPName,"
        << "ot_sip=>:szSIP,"
        << "ot_pid=>:nmPID,"
        << "ot_job=>:nmJOB,"                /* company business code*/
        << "ot_c_job=>:nUrlJob,"            /* callback jobcode */
        << "ot_prt=>:nmPRT,"                /* message priority*/
        << "ot_cnt=>:nmCNT,"                /* transmission count per second*/
        << "ot_rpt_cnt=>:nRptWait,"         /* sleep time when sending continuous rpt */
        << "ot_server_info=>:szServerInfo,"
        << "ot_rpt_sleep_cnt=>:nRptNoDataSleep,"
        << "ot_sender_proc=>:szSenderName,"
        << "ot_report_proc=>:szReportName,"
        << "ot_senderdb_info=>:szSenderDBName,"
        << "ot_reportdb_info=>:szReportDBName,"
        << "ot_logfile_info=>:szLogFilePath,"
        << "ot_etc=>:szReserve,"
        << "ot_rst=>:nmRST,"                /* execution result    */
        << "ot_rstmsg=>:szErrMsg);"         /* sql err msg */
        << "END;";
    
    q->bind(":szAPPName", szAPPName,sizeof(szAPPName));	q->set_bind_output(":szAPPName");
	q->bind(":szSIP", szSIP,sizeof(szSIP));	q->set_bind_output(":szSIP");
	q->bind(":nmPID", nmPID);				q->set_bind_output(":nmPID");
	q->bind(":nmJOB", nmJOB);				q->set_bind_output(":nmJOB");
	q->bind(":nUrlJob", nUrlJob);			q->set_bind_output(":nUrlJob");
	q->bind(":nmPRT", nmPRT);				q->set_bind_output(":nmPRT");
	q->bind(":nmCNT", nmCNT);				q->set_bind_output(":nmCNT");
	q->bind(":nRptWait", nRptWait);			q->set_bind_output(":nRptWait");
    q->bind(":szServerInfo",szServerInfo,sizeof(szServerInfo));	q->set_bind_output(":szServerInfo");
    q->bind(":nRptNoDataSleep",nRptNoDataSleep);	q->set_bind_output(":nRptNoDataSleep");
    q->bind(":szSenderName",szSenderName,sizeof(szSenderName));	q->set_bind_output(":szSenderName");
    q->bind(":szReportName",szReportName,sizeof(szReportName));	q->set_bind_output(":szReportName");
    q->bind(":szSenderDBName",szSenderDBName,sizeof(szSenderDBName));	q->set_bind_output(":szSenderDBName");
    q->bind(":szReportDBName",szReportDBName,sizeof(szReportDBName));	q->set_bind_output(":szReportDBName");
    q->bind(":szLogFilePath",szLogFilePath,sizeof(szLogFilePath));		q->set_bind_output(":szLogFilePath");
    q->bind(":szReserve",szReserve,sizeof(szReserve));			q->set_bind_output(":szReserve");
    q->bind(":nmRST", nmRST);				q->set_bind_output(":nmRST");
    q->bind(":szErrMsg", szErrMsg,sizeof(szErrMsg));			q->set_bind_output(":szErrMsg");
	
    alarm(60); // ?

#if (DEBUG >= 5)
	log_history(0,0,"[DEB] q->bind() after ");
#endif

    if (!q->execute()) 
    {
        log_history(0,0,"[ERR] activeProcess = false, db select failed - [%s]\n", db.error().c_str());
				activeProcess = false;
        alarm(0);
        return -1;
    }
    alarm(0);

#if (DEBUG >= 5)
	log_history(0,0,"[DEB] q->execute() after nmRST[%d]",nmRST);
#endif

    if( nmRST != 0 )
    {
		log_history(0,0,"[ERR] db select failed - proc_check_logon_ext2 nmRST[%d][%s] ErrMsg[%s]",nmRST,logonDbInfo.szCID,szErrMsg); 
		return nmRST;
    }

	memcpy(logonDbInfo.szSIP			,trim(szSIP			,strlen(szSIP))			,sizeof(logonDbInfo.szSIP));
	memcpy(logonDbInfo.szAPPName		,trim(szAPPName		,strlen(szAPPName))		,sizeof(logonDbInfo.szAPPName));
	memcpy(logonDbInfo.szServerInfo		,trim(szServerInfo	,strlen(szServerInfo))	,sizeof(logonDbInfo.szServerInfo));
	logonDbInfo.nRptNoDataSleep = nRptNoDataSleep;
	memcpy(logonDbInfo.szSenderName		,trim(szSenderName	,strlen(szSenderName))	,sizeof(logonDbInfo.szSenderName));
	memcpy(logonDbInfo.szReportName		,trim(szReportName	,strlen(szReportName))	,sizeof(logonDbInfo.szReportName));
	memcpy(logonDbInfo.szSenderDBName	,trim(szSenderDBName,strlen(szSenderDBName)),sizeof(logonDbInfo.szSenderDBName));
	memcpy(logonDbInfo.szReportDBName	,trim(szReportDBName,strlen(szReportDBName)),sizeof(logonDbInfo.szReportDBName));
	memcpy(logonDbInfo.szLogFilePath	,trim(szLogFilePath	,strlen(szLogFilePath))	,sizeof(logonDbInfo.szLogFilePath));
	memcpy(logonDbInfo.szReserve		,trim(szReserve		,strlen(szReserve))		,sizeof(logonDbInfo.szReserve));

	logonDbInfo.nmPID 		= nmPID;
	logonDbInfo.nmJOB 		= nmJOB;
	logonDbInfo.nmPRT 		= nmPRT;
	logonDbInfo.nmCNT 		= nmCNT;
	logonDbInfo.nmRST 		= nmRST;
	logonDbInfo.nUrlJob 	= nUrlJob;
	logonDbInfo.nRptWait 	= nRptWait;

	//* < brief sending limit PROC processing
	nmRST = -999;
	memset(szLimitType,0x00,sizeof(szLimitType));//CCL(szLimitType);
	memset(szLimitFlag,0x00,sizeof(szLimitFlag));//CCL(szLimitFlag);
			
	sprintf(szPID,"%d", logonDbInfo.nmPID);
			
	*q2 << "BEGIN "
		<< "proc_get_limit_def("
		<< "IN_PID=>" << szPID << ","
		<< "OT_LIMIT_TYPE=>:szLimitType,"
		<< "OT_DAY_WARN_CNT=>:nDayWarnCnt,"
		<< "OT_DAY_LIMIT_CNT=>:nDayLimitCnt,"
		<< "OT_MON_WARN_CNT=>:nMonWarnCnt,"
		<< "OT_MON_LIMIT_CNT=>:nMonLimitCnt,"
		<< "OT_LIMIT_FLAG=>:szLimitFlag,"
		<< "OT_DAY_ACC_CNT=>:nDayAccCnt,"
		<< "OT_MON_ACC_CNT=>:nMonAccCnt,"
		<< "OT_RST=>:nmRST,"                /* execution result    */
		<< "OT_RSTMSG=>:szErrMsg);"         /* sql err msg */
		<< "END;";	
		
	q2->bind(":szLimitType"		,szLimitType	,sizeof(szLimitType));
	q2->set_bind_output(":szLimitType");	
	q2->bind(":nDayWarnCnt"		,nDayWarnCnt						);
	q2->set_bind_output(":nDayWarnCnt");
	q2->bind(":nDayLimitCnt"	,nDayLimitCnt						);
	q2->set_bind_output(":nDayLimitCnt");
	q2->bind(":nMonWarnCnt"		,nMonWarnCnt						);
	q2->set_bind_output(":nMonWarnCnt");
	q2->bind(":nMonLimitCnt"	,nMonLimitCnt						);
	q2->set_bind_output(":nMonLimitCnt");
	q2->bind(":szLimitFlag"		,szLimitFlag	,sizeof(szLimitFlag));
	q2->set_bind_output(":szLimitFlag");
	q2->bind(":nDayAccCnt"		,nDayAccCnt							);
	q2->set_bind_output(":nDayAccCnt");
	q2->bind(":nMonAccCnt"		,nMonAccCnt							);
	q2->set_bind_output(":nMonAccCnt");
	q2->bind(":nmRST"			,nmRST								);
	q2->set_bind_output(":nmRST");
	q2->bind(":szErrMsg"		,szErrMsg		,sizeof(szErrMsg)	);
	q2->set_bind_output(":szErrMsg");
	
	alarm(60);
		
	if (!q2->execute()) 
	{
		log_history(0,0,"[ERR] db select failed,activeProcess = false - [%s]\n", db.error().c_str());
		activeProcess = false;
		alarm(0);
		return -1;
	}
	alarm(0);
		
	if( nmRST != 0 )
	{
		log_history(0,0,"[ERR] db select failed - proc_get_limit_def - nmRST[%d] ErrMsg[%s]",nmRST,szErrMsg);
		return nmRST;
	}
	memcpy(logonDbInfo.szLimitType,trim(szLimitType,strlen(szLimitType)),sizeof(logonDbInfo.szLimitType));
	memcpy(logonDbInfo.szLimitFlag,trim(szLimitFlag,strlen(szLimitFlag)),sizeof(logonDbInfo.szLimitFlag));
		
	logonDbInfo.nDayWarnCnt 	= nDayWarnCnt;
	logonDbInfo.nDayLimitCnt 	= nDayLimitCnt;
	logonDbInfo.nMonWarnCnt 	= nMonWarnCnt;
	logonDbInfo.nMonLimitCnt 	= nMonLimitCnt;
	logonDbInfo.nDayAccCnt 		= nDayAccCnt;
	logonDbInfo.nMonAccCnt 		= nMonAccCnt;
	logonDbInfo.classify 		= szClassify[0];

    memset(buff,0x00,SOCKET_BUFF);
    memcpy(buff,&logonDbInfo,sizeof(logonDbInfo));

    return 0;
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;
// read mert conf
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName	,conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	conf.strncpy2(gConf.dbID		,conf.FetchEntry("db.id"),16);
	if( gConf.dbID == NULL )
	{	
		strcpy(gConf.dbID,"");
	}
	conf.strncpy2(gConf.dbPASS		,conf.FetchEntry("db.pass"),16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}
	conf.strncpy2(gConf.dbSID		,conf.FetchEntry("db.sid"),16);
	if( gConf.dbSID == NULL )
	{
	   	strcpy(gConf.dbSID,"");
	}
		
	return 0;
}

int classifyProtocol(ORAPP::Connection &db,CKSSocket& newSockfd,int size,char* bindPacket)
{
    int ret;
    log_history(0,0,"[DEBUG] classifyProtocol checking - bindPacket[%s]", bindPacket);
    if(strstr(bindPacket,"BEGIN CONNECT") != NULL )
    {
        log_history(0,0,"[DEBUG] Found BEGIN CONNECT - calling logonTypeMMS");
        logonTypeMMS(db,newSockfd,1,bindPacket);
        return 0;
    }
    log_history(0,0,"[DEBUG] BEGIN CONNECT not found - protocol not matched");

    switch(size)
    {
        case sizeof(TypeMsgBindSnd):
            logonType(db,newSockfd, 5, bindPacket);
            break;
        case sizeof(TypeMsgBindSnd_v3):
            logonType(db,newSockfd, 3, bindPacket);
            break;
		case sizeof(TypeMsgGetCallback):
	cerr<<__FILE__<<":"<<__LINE__<<endl;
			getCallback(db, newSockfd, 4, bindPacket);
			break;
		case sizeof(TypeMsgGetDialCode):
			getDialCode(db, newSockfd, 4, bindPacket);
			break;
		default:
            log_history(0,0,"classifyProtocol Error : not matching protocol");
            ret = -1;
            break;
    }

    return 0;
}

int logonTypeMMS(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char* bindPacket)
{
    log_history(0,0,"[DEBUG] logonTypeMMS function started - type[%d]", type);
    // Create separate MMS logon procedure.
    // Use the same procedure for now
    // checkLogonMMS function

    int ret=-1;

    TypeMsgBindAck ack;
    memset(&ack	,0x00	,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");
    char code[16];
    char desc[64];

    memset(code, 0x00, sizeof(code));
    memset(desc, 0x00, sizeof(desc));

    log_history(0,0,"[DEBUG] About to call checkLogon - type[%d]", type);
	ret = checkLogon(db,bindPacket,type);
    log_history(0,0,"[DEBUG] checkLogon returned - ret[%d]", ret);
    if( ret < 0 )
    {
        switch(ret)
		{
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                strcpy(code, "300");
                strcpy(desc, "etc");
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                strcpy(code, "200");
                strcpy(desc, "id/pass not found");
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                strcpy(code, "300");
                strcpy(desc, "etc");
                break;
        }
    } 
	else
	{
        memcpy(ack.szResult,"00",2);
        strcpy(code, "100");
        strcpy(desc, "succ");
    }

    char strPacket[256];
    memset(strPacket, 0x00, sizeof(strPacket));
    sprintf(strPacket, "BEGIN ACK\r\nCODE:%s\r\nDESC:%s\r\nEND\r\n", code, desc);

    newSockfd.send(strPacket, strlen(strPacket));

    if( ret < 0 )
  	{
        return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[DOMAIN_RECV_MAX];
    memset(buff,0x00,sizeof(buff));//CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"[ERR] socket_domain read time out");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"[ERR] socket_domain read error [%s]", strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"[ERR] socket_domain read not OK Error[%.2s]",buff);
        return 0;
    }

    ret = newSockfd.send(bindPacket,sizeof(CLogonDbInfo));
    if( ret != sizeof(CLogonDbInfo) )
    {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
	}
    return 0;
}

int logonType(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");
    ret = checkLogon(db,bindPacket,type);
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } else {
        memcpy(ack.szResult,"00",2);
    }

    CLogonDbInfo* pLogonDbInfo = (CLogonDbInfo*)bindPacket;
    printf("logon db [%s]\n",pLogonDbInfo->szCID);
    fflush(stdout);
    if( strcmp(pLogonDbInfo->szCID,"KNBANK") == 0 )
    {
        memcpy(ack.header.msgType,"02",2);
        memcpy(ack.header.msgLeng,"0002",4);
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[DOMAIN_RECV_MAX];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

    ret = newSockfd.send(bindPacket,sizeof(CLogonDbInfo));
    if( ret != sizeof(CLogonDbInfo) )
        log_history(0,0,"logonDbInfo ->session send socket Error[%s]",
                strerror(errno));


    return 0;
}

int getCallback(ORAPP::Connection &db, CKSSocket& newSockfd, int type, char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");

	cerr<<__FILE__<<":"<<__LINE__<<endl;
	set<string> set_callback_list;
    ret = loadCallback(db, bindPacket, type, set_callback_list);
	cerr<<__FILE__<<":"<<__LINE__<<endl;
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } 
	else 
	{
        memcpy(ack.szResult,"00",2);
	cerr<<__FILE__<<":"<<__LINE__<<endl;
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[DOMAIN_RECV_MAX];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

	cerr<<__FILE__<<":"<<__LINE__<<endl;
	set<string>::iterator itr;
	for(itr = set_callback_list.begin(); itr != set_callback_list.end(); itr++)
	{
		string tmp = *itr;
    	ret = newSockfd.send((char*)tmp.c_str(), tmp.size());

		CCL(buff);
    	ret = newSockfd.rcvmsg(buff);

		if( memcmp(buff,"OK",2) != 0 )
		{
			log_history(0,0,"logonDbInfo callback information transmission per item ok reception failed: Error[%.2s]", buff);
			return 0;
		}
	}

    return 0;
}

int loadCallback(ORAPP::Connection &db, char* buff, int type, set<string> &set_callback_list) 
{

    CPacketUtil packetUtil;
    ORAPP::Query *query = db.query();
    CLogonDbInfo logonDbInfo;

	TypeMsgGetCallback* pGetCallback = (TypeMsgGetCallback*)buff;
	
	char qry[1024];
	sprintf(qry, "SELECT PTN_ID, CALLBACK FROM TBL_CALLBACK WHERE PTN_ID = %d", pGetCallback->pid);
	*query << qry;

	log_history(0, 0,"Executing %s", qry);

	if (!query->execute()) 
    {
        log_history(0,0,"[ERR] activeProcess = false, db select failed - [%s]\n", db.error().c_str());
		activeProcess = false;
        alarm(0);
        return -1;
    }

	alarm(0);

	ORAPP::Row *r;
	cerr<<__FILE__<<":"<<__LINE__<<endl;

	while ((r = query->fetch())) {
	cerr<<__FILE__<<":"<<__LINE__<<endl;
		printf("::: rows fetched = %u, width = %u\n", query->rows(), r->width());

		int i;
		for (i = 0; i < r->width(); i++)
			printf(":::    row%u named %s\n", i, r->name(i));

		int ptn_id = (int)(*r)["ptn_id"];
		char *callback = (char*)(*r)["callback"];
		trim(callback, strlen(callback));

		printf("ptn_id = [%d], callback = [%s]", ptn_id, callback);

		char key[128]; 
	
		sprintf(key, "%s", callback);

		set_callback_list.insert(key);
	}
	cerr<<__FILE__<<":"<<__LINE__<<endl;

    return 0;
}

int getDialCode(ORAPP::Connection &db, CKSSocket& newSockfd, int type, char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");

	set<string> set_dialcode_list;
    ret = loadDialCode(db, bindPacket, type, set_dialcode_list);
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } else {
        memcpy(ack.szResult,"00",2);
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[DOMAIN_RECV_MAX];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

	set<string>::iterator itr;
	for(itr = set_dialcode_list.begin(); itr != set_dialcode_list.end(); itr++)
	{
		string tmp = *itr;
    	ret = newSockfd.send((char*)tmp.c_str(), tmp.size());

		CCL(buff);
    	ret = newSockfd.rcvmsg(buff);

		if( memcmp(buff,"OK",2) != 0 )
		{
			log_history(0,0,"logonDbInfo callback information transmission per item ok reception failed: Error[%.2s]", buff);
			return 0;
		}
	}

    return 0;
}

int loadDialCode(ORAPP::Connection &db, char* buff, int type, set<string> &set_dialcode_list) 
{
    CPacketUtil packetUtil;
    ORAPP::Query *query = db.query();
    CLogonDbInfo logonDbInfo;

	TypeMsgGetDialCode* pGetDialCode = (TypeMsgGetDialCode*)buff;
	
	char qry[1024];
	sprintf(qry, "SELECT DIAL_CODE_TYPE, DIAL_CODE FROM TBL_ALLOW_DIAL_CODE WHERE DIAL_CODE_TYPE = %s"
			, pGetDialCode->dial_code_type);
	
	*query << qry;

	log_history(0, 0,"Executing %s", qry);

	if (!query->execute()) 
    {
        log_history(0,0,"[ERR] activeProcess = false, db select failed - [%s]\n", db.error().c_str());
		activeProcess = false;
        alarm(0);
        return -1;
    }

	alarm(0);

	ORAPP::Row *r;

	while ((r = query->fetch())) {
		printf("::: rows fetched = %u, width = %u\n", query->rows(), r->width());

		int i;
		for (i = 0; i < r->width(); i++)
			printf(":::    row%u named %s\n", i, r->name(i));

		char *dial_code_type = (char*)(*r)["dial_code_type"];
		char *dial_code = (char*)(*r)["dial_code"];
		trim(dial_code, strlen(dial_code));

		printf("dial_code_type = [%s], dial_code = [%s]", dial_code_type, dial_code);

		char key[128]; 
	
		sprintf(key, "%s", dial_code);

		set_dialcode_list.insert(key);
	}

    return 0;
}


