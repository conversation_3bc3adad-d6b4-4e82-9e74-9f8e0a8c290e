cmake_minimum_required(VERSION 3.16)
project(daemon_logon_ntk)

set(CMAKE_CXX_STANDARD 11)

# Set default build type to Debug for better debugging support
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build" FORCE)
    message(STATUS "daemon_logon_ntk: Build type set to Debug")
endif()

# CLion build flag configuration (optional feature)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_logon_ntk: CLion build mode enabled")
endif()

# Enable compile commands for better IDE support
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(DEBUG 5 CACHE STRING "Debug level (0=off, 5=verbose)")
add_definitions(-DDEBUG=${DEBUG})

# Database configuration
# Load database config from file if exists
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# Read database config from environment variables (takes precedence over file)
if(DEFINED ENV{DBSTRING})
    set(DBSTRING "$ENV{DBSTRING}")
endif()
if(DEFINED ENV{DBID})
    set(DBID "$ENV{DBID}")
endif()
if(DEFINED ENV{DBPASS})
    set(DBPASS "$ENV{DBPASS}")
endif()

# Set default values if not defined
if(NOT DEFINED DBSTRING OR DBSTRING STREQUAL "")
    set(DBSTRING "NEO223")
    message(STATUS "Using default DBSTRING: ${DBSTRING}")
endif()
if(NOT DEFINED DBID OR DBID STREQUAL "")
    set(DBID "neontk")
    message(STATUS "Using default DBID: ${DBID}")
endif()
if(NOT DEFINED DBPASS OR DBPASS STREQUAL "")
    set(DBPASS "kskybDB0955**")
    message(STATUS "Using default DBPASS: [hidden]")
endif()

message(STATUS "Database configuration loaded successfully")

# Oracle configuration - try multiple possible locations
set(ORACLE_HOME_CANDIDATES
    "$ENV{ORACLE_HOME}"
    "/usr/lib/oracle/21/client64"
    "/usr/lib/oracle/19/client64"
    "/usr/lib/oracle/18/client64"
    "/usr/lib/oracle/12.2/client64"
)

foreach(candidate ${ORACLE_HOME_CANDIDATES})
    if(EXISTS "${candidate}" AND IS_DIRECTORY "${candidate}")
        set(ORACLE_HOME "${candidate}")
        break()
    endif()
endforeach()

if(NOT ORACLE_HOME)
    message(WARNING "Oracle client not found. Oracle-dependent executables will not be built.")
    set(ORACLE_HOME "")
else()
    # Set Oracle paths
    set(PROC_INCLUDE "${ORACLE_HOME}/include")
    if(NOT EXISTS "${PROC_INCLUDE}")
        set(PROC_INCLUDE "/usr/include/oracle/21/client64")
        if(NOT EXISTS "${PROC_INCLUDE}")
            set(PROC_INCLUDE "${ORACLE_HOME}/precomp/public")
        endif()
    endif()

    set(PROC_CONFIG "${ORACLE_HOME}/lib/precomp/admin/pcscfg.cfg")
    if(NOT EXISTS "${PROC_CONFIG}")
        set(PROC_CONFIG "${ORACLE_HOME}/precomp/admin/pcscfg.cfg")
    endif()

    # Set Oracle environment variables
    set(ENV{ORACLE_HOME} "${ORACLE_HOME}")
    set(ENV{LD_LIBRARY_PATH} "${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}")
    set(ENV{TNS_ADMIN} "${ORACLE_HOME}/network/admin")

    message(STATUS "Oracle configuration:")
    message(STATUS "  ORACLE_HOME: ${ORACLE_HOME}")
    message(STATUS "  PROC_INCLUDE: ${PROC_INCLUDE}")
    message(STATUS "  PROC_CONFIG: ${PROC_CONFIG}")

    # Find Oracle Pro*C compiler
    find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin NO_DEFAULT_PATH)
    if(NOT PROC_EXECUTABLE)
        find_program(PROC_EXECUTABLE proc)
        if(NOT PROC_EXECUTABLE)
            message(WARNING "Oracle Pro*C compiler not found. Pro*C executables will not be built.")
        endif()
    endif()

    if(PROC_EXECUTABLE)
        message(STATUS "Using Oracle Pro*C compiler: ${PROC_EXECUTABLE}")
    endif()
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -lrt -w -DDEBUG=5")
#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-exceptions -fno-rtti -D_REENTRANT=1")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti -D_REENTRANT=1")

# Oracle Pro*C preprocessing function
function(add_proc_source target_name source_file)
    if(NOT PROC_EXECUTABLE OR NOT ORACLE_HOME)
        message(WARNING "Oracle Pro*C not available, skipping ${source_file}")
        return()
    endif()

    get_filename_component(source_name ${source_file} NAME_WE)
    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # Copy .cpp to .pc file
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # Special options for certain files
    if(source_name MATCHES "logonDB|logonSession")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # Oracle Pro*C preprocessing
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:${ORACLE_HOME}/plsql/lib:${ORACLE_HOME}/network/lib"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            "PATH=${ORACLE_HOME}/bin:$ENV{PATH}"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/inc
            include=$ENV{HOME}/library
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            define=__sparc
            SQLCHECK=SEMANTICS
            userid=${DBID}/${DBPASS}@${DBSTRING}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Add orapp library as subdirectory for debugging support
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/orapp/CMakeLists.txt)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/orapp ${CMAKE_CURRENT_BINARY_DIR}/orapp)
    message(STATUS "Added orapp library as subdirectory for debugging support")
endif()

# Add libkskyb library as subdirectory for debugging support
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/CMakeLists.txt)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb ${CMAKE_CURRENT_BINARY_DIR}/libkskyb)
    message(STATUS "Added libkskyb library as subdirectory for debugging support")
endif()

# Include directories
include_directories(
    inc
    lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/orapp
    $ENV{HOME}/library
    /usr/include
    /usr/local/openssl/include
)

if(ORACLE_HOME)
    include_directories(${PROC_INCLUDE})
endif()

# Link directories
link_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/lib
    /usr/lib64
    /usr/local/openssl/lib
    $ENV{HOME}/library
)

if(ORACLE_HOME)
    link_directories(
        ${ORACLE_HOME}/lib
        ${ORACLE_HOME}/plsql/lib
        ${ORACLE_HOME}/network/lib
    )
endif()

# Common definitions
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# Build common library sources if they exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    file(GLOB LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/lib/*.cpp")
    # Exclude DatabaseORA_MMS.cpp as it needs Pro*C processing
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*DatabaseORA_MMS\\.cpp$")
    # Exclude command_util.cpp if it causes issues (like in original makefile)
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*command_util\\.cpp$")

    # Debug: Show which files are included
    message(STATUS "Library sources to build: ${LIB_SOURCES}")

    if(LIB_SOURCES)
        add_library(daemon_logon_ntk_lib STATIC ${LIB_SOURCES})
        target_include_directories(daemon_logon_ntk_lib PRIVATE
            inc
            ${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/inc
            ${CMAKE_CURRENT_SOURCE_DIR}/../libsrc/libkskyb/inc
            $ENV{HOME}/library
        )
    endif()
endif()

# DatabaseORA_MMS Oracle Pro*C library
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp AND PROC_EXECUTABLE)
    add_library(database_ora_mms STATIC)
    add_proc_source(database_ora_mms ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
endif()

# Define executables
set(EXECUTABLES
    logonSession
    logonDB
    admin
    monitorProcess
    adminProcess
    reportMMSProcDB
    senderNtalkProDB
)

# Create executables
foreach(exec_name ${EXECUTABLES})
    set(source_file "${CMAKE_CURRENT_SOURCE_DIR}/src/${exec_name}.cpp")
    
    # Handle special cases for file names
    if(exec_name STREQUAL "reportMMSProcDB")
        set(source_file "${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp")
    endif()
    
    if(EXISTS ${source_file})
        add_executable(${exec_name})
        
        # Check if file contains EXEC SQL for Pro*C processing
        file(READ ${source_file} FILE_CONTENT)
        string(FIND "${FILE_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)
        
        if(EXEC_SQL_FOUND GREATER -1 AND PROC_EXECUTABLE)
            # Process with Pro*C
            add_proc_source(${exec_name} ${source_file})
        else()
            # Regular C++ compilation
            target_sources(${exec_name} PRIVATE ${source_file})
        endif()
    else()
        message(WARNING "Source file not found for ${exec_name}: ${source_file}")
    endif()
endforeach()

# Link libraries for each executable
foreach(exec_name ${EXECUTABLES})
    if(TARGET ${exec_name})
        # Link common library
        if(TARGET daemon_logon_ntk_lib)
            target_link_libraries(${exec_name} daemon_logon_ntk_lib)
        endif()
        
        # Link DatabaseORA_MMS library
        if(TARGET database_ora_mms)
            target_link_libraries(${exec_name} database_ora_mms)
        endif()
        
        # Link external object file (except for admin)
        if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/obj/sms_ctrlsub++.o AND NOT exec_name STREQUAL "admin")
            target_link_libraries(${exec_name} ${CMAKE_CURRENT_SOURCE_DIR}/../command_logon_ntk/obj/sms_ctrlsub++.o)
        endif()
        
        # Link KSkyB libraries (built from source if available, otherwise prebuilt)
        if(TARGET libkssocket)
            target_link_libraries(${exec_name}
                libksbase64
                libkssocket
                libksconfig
                libksthread
            )
            # Add dependency to ensure libkskyb is built before this target
            add_dependencies(${exec_name} libkssocket libksbase64 libksconfig libksthread)
            message(STATUS "Linking ${exec_name} with libkskyb source libraries")
        else()
            target_link_libraries(${exec_name}
                ksbase64
                kssocket
                ksconfig
                ksthread
            )
            message(STATUS "Linking ${exec_name} with prebuilt libkskyb libraries")
        endif()
        
        # Link Oracle libraries for Oracle-dependent executables
        if(ORACLE_HOME AND exec_name MATCHES "logonDB|logonSession|sender.*|report.*")
            # Link orapp library (built from source if available, otherwise prebuilt)
            if(TARGET liborapp)
                target_link_libraries(${exec_name} clntsh dl liborapp)
            else()
                target_link_libraries(${exec_name} clntsh dl orapp)
            endif()
            # Set RPATH for Oracle libraries
            set_target_properties(${exec_name} PROPERTIES
                INSTALL_RPATH "${ORACLE_HOME}/lib"
                BUILD_WITH_INSTALL_RPATH TRUE
            )
        endif()
        
        # Link system libraries
        target_link_libraries(${exec_name}
            pthread
            crypto
            nsl
            dl
        )
        
        # Set output directory
        set_target_properties(${exec_name} PROPERTIES
            RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
        )
    endif()
endforeach()

# Create database config template
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake.template")
    file(WRITE "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake.template"
"# Database configuration template
# Copy this file to db_config.cmake and set your values

set(DBSTRING \"NEO223\")
set(DBID \"neontk\")
set(DBPASS \"your_password_here\")
")
endif()

# Custom targets for makefile compatibility
add_custom_target(makefile_build_daemon_logon_ntk
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building daemon_logon_ntk with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

add_custom_target(makefile_clean_daemon_logon_ntk
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning daemon_logon_ntk with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Debug information
message(STATUS "daemon_logon_ntk CMake configuration completed")
message(STATUS "Executables to build: ${EXECUTABLES}")
if(ORACLE_HOME)
    message(STATUS "Oracle support: ENABLED")
else()
    message(STATUS "Oracle support: DISABLED")
endif()