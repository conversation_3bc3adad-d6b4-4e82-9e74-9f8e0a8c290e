#ifndef _ENCRYPT_H_
#define _ENCRYPT_H_
#include <stdlib.h>
#include <cstdio>
#include <cstring>
#include <iostream>
using namespace std;

#include <openssl/aes.h>

#define KEY_SIZE 128
//#define BYTES_SIZE 4096
#define BYTES_SIZE 8192

struct ctr_state {
	unsigned char ivec[AES_BLOCK_SIZE];
	unsigned int num;
	unsigned char ecount[AES_BLOCK_SIZE];
};

class Encrypt 
{
public:
	void set_key();
	void init_ctr(struct ctr_state *state, const unsigned char *iv);
	void encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	void decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	
	
	AES_KEY ase_key;
	unsigned char iv[16];
	unsigned char ckey[16];
};
#endif
