/*****************************************************************************
 * File Name   : mo_common.h
 * Author      : kskyb.com
 * Date        : 2008.09.08
 * Description : Common Header & Define
 *****************************************************************************/
#ifndef _MO_COMMON_H_
#define _MO_COMMON_H_

#define MOVERSION "*******"
#define MOOS "linux"

#define INTERVAL_LINK_SEND 15

typedef struct _config {
    char logLogwrite;
    char logLogdir[256];
    char commResttime[256];
    int  clientListenport;
    int  clientRetryinterval;
    char clientReportackondbfail;
    char clientLogwrite;
    char clientLogdir[256];
    char clientMailServer[32];
    int  clientMailPort;
    char client<PERSON>ailAddress[128];
    char clientJobCode[10];
    char clientCpCode[15];
    char clientSenderProcessName[64];
    char clientReportProcessName[64];
    char gwAddress[256];
    int  gwPort;
    char gwId[32];
    char gwPassword[32];
    int  gwPinginterval;
    char dbType[16];
    char dbHost[64];
    char dbUser[64];
    char dbDatabase[64];
    char dbPassword[64];
    int  dbSendcount;
    int  dbMovecount;
    int  dbPollinginterval;
    int  dbRecordfile;
    int  dbMoveonresult;
    int  dbMoveinginterval;
    char dbDatabackup[256];
    char dbMoveExecTime[10];
    int  dbdatacheck;
    int  clientmsgType;
    int  dbgetmaxcount;
    char packetReserve[20];
    int  dbtimeout;
} ConfMO;

typedef struct _TYPE_HEADER {
	char msgType[2];
	char msgLeng[4];
} TypeHeader;

typedef struct _TYPE_MSG_BIND_SND {
	TypeHeader header;
	char szCID[10];
	char szPWD[10]; 
	char szOS[10];
	char szVER[10];
	char classify;
} TypeMsgBindSnd; /* size 27 = Header size 6 + Body size 21 */

typedef struct _TYPE_MSG_BIND_ACK {
	TypeHeader header;
	char szResult[2];
} TypeMsgBindAck; /* size 8 = Header size 6 + Body size 2 */

typedef struct _RECV_DATA_MO {
	TypeHeader header;
	char szSerial[16];
	char szMoPhone[16];
	char szCalbak[16];
	char szDstadr[16];
	char szMomsg[128];
	char szMoDate[16];
} RcvDataMo; /* size 214 = Header size 6 + Body size 208 */

typedef struct _RECV_DATA_ACK2 {
	TypeHeader header;
	char szSerial[16];
	char szResult[2];
} RcvDataAck2;  /* size 24 = Header size 6 + Body size 18 */


#define SIZE_TYPE_MSG_BIND_SND  sizeof(TypeMsgBindSnd)
#define SIZE_TYPE_MSG_BIND_ACK  sizeof(TypeMsgBindAck)

char* trim(char* szOrg, int leng);
void Init_Server();
void wait_a_moment(int nSec, int nUsc);
void logPrint(const char *logMsg, ...);

int PutMsgQBC(int msg_qid, int nOrder, char *pData, int nSize);
int GetMsgQBC(int msg_qid, int nOrder, char *pData, int nSize);
int ConfigParse(ConfMO *pConf, char *pszConf);

int ConnServerMO(char* hostAddr, int hostPort, char c_type);
void CloseServerMO(int sockfd);
int UnixSocketRecv(int sockfd, char *TcpBuf, int getLength);

void monitoring(char *buf, int st, int err);
void logmessage(char *buf, int st, int err);

#endif //_MO_COMMON_H_
