# CMakeLists.txt for daemon_sky_ntk
cmake_minimum_required(VERSION 3.10)
project(daemon_sky_ntk CXX)

set(CMAKE_CXX_STANDARD 11)

# CLion build flag configuration (optional for IDE)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_sky_ntk: CLion build mode enabled")
endif()

# Directory path configuration
message(STATUS "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: ${CMAKE_CURRENT_SOURCE_DIR}")

# Oracle environment configuration
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Database configuration file loading
# Option 1: Environment variables (for CI/CD environments)
# Option 2: Local configuration file (for development)

# Include database config if exists
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# Read database configuration from environment variables (takes precedence over config file)
if(DEFINED ENV{SMS_DBSTRING})
    set(SMS_DBSTRING "$ENV{SMS_DBSTRING}")
endif()
if(DEFINED ENV{SMS_DBID})
    set(SMS_DBID "$ENV{SMS_DBID}")
endif()
if(DEFINED ENV{SMS_DBPASS})
    set(SMS_DBPASS "$ENV{SMS_DBPASS}")
endif()

# Database connection option (default OFF for safe development environment)
option(ENABLE_DB_CONNECTION "Enable database connection for Pro*C compilation" ON)

# SQLCHECK mode configuration (environment variable takes precedence)
if(DEFINED ENV{PROC_SQLCHECK})
    set(PROC_SQLCHECK "$ENV{PROC_SQLCHECK}")
elseif(ENABLE_DB_CONNECTION)
    set(PROC_SQLCHECK "SEMANTICS")
else()
    set(PROC_SQLCHECK "SYNTAX")  # Default: SYNTAX (no database connection required)
endif()

# Required configuration validation (for SEMANTICS mode only)
if(PROC_SQLCHECK STREQUAL "SEMANTICS")
    if(NOT DEFINED SMS_DBSTRING OR SMS_DBSTRING STREQUAL "" OR SMS_DBSTRING STREQUAL "your_sms_database_string")
        message(FATAL_ERROR "SMS_DBSTRING not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBID OR SMS_DBID STREQUAL "" OR SMS_DBID STREQUAL "your_sms_database_id")
        message(FATAL_ERROR "SMS_DBID not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBPASS OR SMS_DBPASS STREQUAL "" OR SMS_DBPASS STREQUAL "your_sms_database_password")
        message(FATAL_ERROR "SMS_DBPASS not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    message(STATUS "SQLCHECK=SEMANTICS mode enabled - database connection required")
else()
    message(STATUS "SQLCHECK=SYNTAX mode enabled - no database connection required")
endif()

# Configuration loading confirmation (without exposing sensitive information)
message(STATUS "Database configuration loaded successfully")

# Directory configuration
set(ORG_D ${CMAKE_CURRENT_SOURCE_DIR})
set(BIN_D ${ORG_D}/bin)
set(OBJ_D ${ORG_D}/obj)
set(LIB_D ${ORG_D}/lib)
set(INC_D ${ORG_D}/inc)
set(SRC_D ${ORG_D}/src)

# External include directory path configuration
if(EXISTS "${CMAKE_SOURCE_DIR}/command_kskyb_ntk/inc")
    # Running from main project
    set(EXT_INC "${CMAKE_SOURCE_DIR}/command_kskyb_ntk/inc")
    message(STATUS "Include location: main project")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_kskyb_ntk/inc")
    # Running from standalone project
    set(EXT_INC "${CMAKE_CURRENT_SOURCE_DIR}/../command_kskyb_ntk/inc")
    message(STATUS "Include location: standalone project")
else()
    # CLion project or other case (use NEONTK_230_ROOT_PATH variable if available)
    if(DEFINED NEONTK_230_ROOT_PATH)
        set(EXT_INC "${NEONTK_230_ROOT_PATH}/command_kskyb_ntk/inc")
    else()
        set(EXT_INC "/home/<USER>/CLionProjects/neontk_230/command_kskyb_ntk/inc")
    endif()
    message(STATUS "Include location: CLion or fallback path")
endif()

# External library object path configuration
if(EXISTS "${CMAKE_SOURCE_DIR}/command_kskyb_ntk/obj/sms_ctrlsub++.o")
    # Running from main project
    set(EXT_LIB "${CMAKE_SOURCE_DIR}/command_kskyb_ntk/obj/sms_ctrlsub++.o")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_kskyb_ntk/obj/sms_ctrlsub++.o")
    # Running from standalone project
    set(EXT_LIB "${CMAKE_CURRENT_SOURCE_DIR}/../command_kskyb_ntk/obj/sms_ctrlsub++.o")
else()
    # CLion project or other case (use NEONTK_230_ROOT_PATH variable if available)
    if(DEFINED NEONTK_230_ROOT_PATH)
        set(EXT_LIB "${NEONTK_230_ROOT_PATH}/command_kskyb_ntk/obj/sms_ctrlsub++.o")
    else()
        set(EXT_LIB "/home/<USER>/CLionProjects/neontk_230/command_kskyb_ntk/obj/sms_ctrlsub++.o")
    endif()
    message(STATUS "Library location: CLion or fallback path (external object)")
endif()

# Path configuration confirmation
message(STATUS "EXT_INC: ${EXT_INC}")
message(STATUS "EXT_LIB: ${EXT_LIB}")

# Find Oracle Pro*C compiler
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C compiler not found. Please check ORACLE_HOME environment variable.")
endif()

# Compiler definitions and flags
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# DEBUG mode configuration (enables debug output when DEBUG >= 5)
option(ENABLE_DEBUG "Enable debug output (DEBUG >= 5)" OFF)

# Debug flag configuration based on build type
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug build: DEBUG=5 enabled")
elseif(ENABLE_DEBUG)
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug mode manually enabled (DEBUG=5)")
else()
    add_definitions(-DDEBUG=0)
    message(STATUS "Release build: DEBUG=0 (debug disabled)")
endif()

# OpenSSL version configuration
option(ENABLE_SSL_V3 "Enable OpenSSL 3.0+ EVP API (default: ON for compatibility)" ON)

if(ENABLE_SSL_V3)
    add_definitions(-DSSL_V3)
    message(STATUS "OpenSSL 3.0+ EVP API enabled (SSL_V3 defined)")
else()
    message(STATUS "Legacy OpenSSL API enabled (SSL_V3 not defined)")
endif()

# Compiler flags configuration
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")

# Oracle Pro*C preprocessing function
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # Copy .cpp to .pc
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # SQLCHECK option and userid configuration
    if(ENABLE_DB_CONNECTION AND PROC_SQLCHECK STREQUAL "SEMANTICS")
        # Production mode: use actual database connection
        set(SQLCHECK_OPTION "SQLCHECK=SEMANTICS")
        set(USERID_OPTION "userid=${SMS_DBID}/${SMS_DBPASS}@${SMS_DBSTRING}")
        set(PARSE_OPTION "PARSE=NONE")
        set(DEFINE_OPTIONS "define=ORACLE_PROC_COMPILE")
    else()
        # Development mode: no database connection required
        set(SQLCHECK_OPTION "SQLCHECK=SYNTAX")
        set(USERID_OPTION "")
        set(PARSE_OPTION "PARSE=NONE")
        set(DEFINE_OPTIONS "")
    endif()

    # Oracle Pro*C preprocessing
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            "PATH=${ORACLE_HOME}/bin:$ENV{PATH}"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${INC_D}
            include=${PROC_INCLUDE}
            include=${EXT_INC}
            include=${LIB_D}
            CPP_SUFFIX=cpp
            CODE=CPP
            ${PARSE_OPTION}
            CTIMEOUT=3
            MAXOPENCURSORS=10
            HOLD_CURSOR=YES
            RELEASE_CURSOR=YES
            define=__sparc
            ${DEFINE_OPTIONS}
            config=${PROC_CONFIG}
            ${SQLCHECK_OPTION}
            ${USERID_OPTION}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Include directories
include_directories(
    ${INC_D}
    ${LIB_D}
    ${PROC_INCLUDE}
    ${EXT_INC}
)

# Library directories
link_directories(
    ${ORACLE_HOME}/lib
    ${ORACLE_HOME}/plsql/lib
    ${ORACLE_HOME}/network/lib
    /usr/lib64
)

# Common library (Oracle Pro*C not required)
add_library(sky_ntk_lib STATIC
    ${LIB_D}/Properties.cpp
    ${LIB_D}/myException.cpp
    ${LIB_D}/SocketTCP.cpp
    ${LIB_D}/PacketCtrlSKY.cpp
    ${LIB_D}/ksbase64.cpp
    ${LIB_D}/base64.cpp
    ${LIB_D}/Encrypt.cpp
    ${LIB_D}/KISA_SEED_CBC.cpp
    ${LIB_D}/seed.cpp
)

# DatabaseORA (Oracle Pro*C required)
add_library(database_ora STATIC)
add_proc_source(database_ora ${LIB_D}/DatabaseORA.cpp)

# Executable - telco_sky_new
add_executable(telco_sky_new)
add_proc_source(telco_sky_new ${SRC_D}/telco_sky_new.cpp)

# Link configuration
target_link_libraries(telco_sky_new
    sky_ntk_lib
    database_ora
    ${EXT_LIB}
    clntsh
    pthread
    nsl
    dl
    crypto
)

# Output directory configuration
set_target_properties(telco_sky_new PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${BIN_D}"
    OUTPUT_NAME "telco_ntk_sky"
)
